# Multi-stage Dockerfile for Bun-based Vite React Application
# Stage 1: Build stage using Bun
FROM oven/bun:1.1.38-alpine AS builder

# Set working directory
WORKDIR /app

# Install system dependencies for native modules and CKEditor build
# These are needed for building native dependencies and CKEditor
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl \
    && ln -sf python3 /usr/bin/python

# Copy package files for dependency installation
# This allows <PERSON><PERSON> to cache the dependency installation layer
COPY package.json bun.lock* ./

# Copy CKEditor custom build configuration
# CKEditor needs to be built before the main application
COPY ckeditor5/ ./ckeditor5/

# Install dependencies using Bun
# Use --frozen-lockfile to ensure reproducible builds
RUN bun install --frozen-lockfile

# Build custom CKEditor first (required dependency)
# CKEditor must be built before the main application can be built
WORKDIR /app/ckeditor5
RUN bun install --frozen-lockfile && bun run build

# Return to main app directory
WORKDIR /app

# Copy source code (excluding files in .dockerignore)
COPY . .

# Build the application for production
# This creates optimized static files in the build/ directory
RUN bun run build

# Verify build output exists
RUN ls -la build/ && echo "Build completed successfully"

# Stage 2: Production stage using Nginx
FROM nginx:1.25-alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Remove default nginx website
RUN rm -rf /usr/share/nginx/html/*

# Copy built application from builder stage
COPY --from=builder /app/build /usr/share/nginx/html

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create nginx user and set permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d

# Switch to non-root user for security
USER nginx

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
